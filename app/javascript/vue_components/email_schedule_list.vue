<template>
  <div class="data-schedule-list vue-datatable-wrapper ci-data-schedule-list">
    <div v-show="!loaded">
      <span>Loading</span>
      <h-icon
        name="circle-notch"
        spin
      />
    </div>
    <p v-show="showManageRecipients">
      <HButton
        type="primary-highlight"
        @click="listRecipients"
      >
        Manage Email Recipients
      </HButton>
    </p>
    <h-table
      v-show="loaded"
      :data="decoratedSchedules"
      :columns="columns"
      skin="table-normal"
      :options="options"
    >
      <template #data_schedule_title="props">
        <div class="flex items-center">
          <HTooltip
            v-if="isExcelPasswordProtected(props.row)"
            :content="'Password Protected'"
          >
            <h-icon
              name="lock"
              class="ci-password-protected mr-1"
            />
          </HTooltip>
          <data-schedule-cell
            :data="props.row.data_schedule_title"
            :dest-type="props.row.dest_type"
            cell-type="data_schedule_title"
            data-ci="ci-schedule-title-display"
          />
        </div>
      </template>
      <template #schedule_title="props">
        <schedule-label
          :schedule="props.row.schedule"
          :source-id="props.row.id"
          @update-schedule="onScheduleUpdated"
        />
      </template>
      <template #destination="props">
        <data-schedule-cell
          :data="props.row.dest"
          :dest-type="props.row.dest_type"
          cell-type="destinations"
        />
      </template>
      <template #source_title="props">
        <div>
          <h-icon :name="props.row.source_info.icon" />
          <a
            :href="props.row.source_info.href"
            class="text-link"
            target="_blank"
          >{{ props.row.source_title }}</a>
        </div>
      </template>
      <template #filters="props">
        <div v-if="props.row.is_v3">
          (Not available)
        </div>
        <div v-else>
          <p v-if="props.row.filter_values && props.row.filter_values.length === 0">
            (No filters)
          </p>
          <div
            v-for="filter in props.row.filter_values"
            :key="filter.filter_ownership_id"
          >
            <HTooltip
              v-if="filter.type !== 'separator'"
              :disabled="filter.override || filter.error"
              content="Default value"
              placement="right"
            >
              <span :class="{'filter-value': true, 'use-default': !filter.override, error: filter.error}">
                <b>- {{ filter.label || '(No label)' }}</b>:
                <span>{{ filter.display_value }}</span>
              </span>
            </HTooltip>
          </div>
        </div>
      </template>
      <template #last_run="props">
        <last-run
          v-model="props.row.last_run_job"
          :source-type="'EmailSchedule'"
          source-method="execute"
          :source-id="props.row.id"
        />
      </template>
      <template #actions="{ row }">
        <HDropdown
          :options="[
            { key: 'send', label: 'Send', icons: 'send', class: 'ci-es-send', action: () => sendDs(row.id) },
            { key: 'edit', label: 'Edit', icons: 'edit', class: 'ci-es-edit', action: () => editDs(row.id) },
            { key: 'delete', slot: 'delete' },
          ]"
          class="!float-right"
        >
          <HButton
            type="tertiary-default"
            unified
            icon="ellipsis-horizontal"
            size="sm"
            class="ci-actions"
          />

          <template #delete="{ onClose }">
            <FloatingDeletePanel
              :title="'id ' + row.id"
              :type="'email schedule'"
              class="!block"
              @delete-panel="onClose(true); deleteDs(row)"
            >
              <div class="ci-es-delete flex cursor-pointer items-start space-x-1 rounded p-2 text-red-500 hover:bg-gray-100 active:bg-gray-400">
                <h-icon
                  name="delete"
                  class="mr-1"
                />
                Delete
              </div>
            </FloatingDeletePanel>
          </template>
        </HDropdown>
      </template>
    </h-table>
    <div
      v-if="loaded && source"
      class="actions mt-2 flex items-center"
    >
      <template v-if="isV3">
        <HButton
          type="primary-highlight"
          icon="add"
          class="ci-new-schedule-btn"
          @click.prevent="newDynamicSchedule()"
        >
          New Schedule
        </HButton>
      </template>

      <template v-else>
        <HDropdown
          v-if="DestList(source).length > 1"
          ref="newSchedule"
          :options="DestList(source).map((dest) => ({
            key: dest.type,
            slot: dest.createButton ? dest.type : undefined,
            label: dest.displayName,
            icons: dest.icon,
            class: `ci-${dest.type.toLowerCase()}-schedule`,
            action: () => newDs(dest.type)
          }))
          "
          placement="top-start"
        >
          <HButton
            type="primary-highlight"
            icon="add"
            class="ci-new-schedule-btn dropdown-toggle"
          >
            New Schedule
          </HButton>

          <template
            v-for="dest in DestList(source).filter((dest) => dest.createButton)"
            :key="dest.type"
            #[dest.type]
          >
            <!-- TODO: Restyle links for Dropdown Option -->
            <component
              :is="dest.createButton"
              :source="source"
              :on-created="refresh"
              class="flex cursor-pointer items-start rounded p-2 hover:bg-gray-100 active:bg-gray-400"
              :class="`ci-${dest.type.toLowerCase()}-schedule`"
              @on-close="() => { $refs?.newSchedule?.onClose() }"
            />
          </template>
        </HDropdown>

        <span
          v-for="Dest in DestList(source)"
          v-else
          :key="Dest.type"
          :class="`ci-${Dest.type.toLowerCase()}-schedule`"
        >
          <component
            :is="Dest.createButton"
            v-if="Dest.createButton"
            :source="source"
            :on-created="refresh"
          />
          <HButton
            v-else
            type="primary-highlight"
            :icon="Dest.icon"
            @click="newDs(Dest.type)"
          >
            New {{ Dest.displayName }} Schedule
          </HButton>
        </span>
      </template>
      <HButton
        type="secondary-default"
        class="ml-2"
        @click="sendAll()"
      >
        Send all
      </HButton>
    </div>
  </div>
</template>
<script>
import { HTooltip, HButton, HDropdown } from '@holistics/design-system';
import { mapGetters } from 'vuex';
import {
  filter, isObject, get, find, noop,
} from 'lodash';
import Schedule from '@/es6/schedule';
import TableHelper from '@/es6/vue_datatable_helper';
import * as Ajax from '@/core/services/ajax';
import * as Notifier from '@/core/services/notifier';
import Modal from '@/ui/modals';
import slackScheduleButton from '@/vue_components/data_schedules/slack_schedule_button.vue';
import DataScheduleEditModal from '@/ui/modals/data_schedule_edit';
import ScheduleEditModal from '@/ui/modals/schedule_edit';
import emailScheduleRecipientModal from '@/ui/modals/email_schedule_recipient';
import Jobs from '@/jobs/jobs';
import LastRun from '@/vue_components/last_run.vue';
import dataScheduleCell from '@/vue_components/data_schedules/data_schedule_cell.vue';
import scheduleLabel from '@/vue_components/schedule/schedule_label.vue';
import EmailSchedules from '@/es6/email_schedules';
import { EMAIL_DEST_TYPE, DEST_LIST } from '@/modules/DataSchedule/constants/dests';
import FloatingDeletePanel from '@/core/components/ui/FloatingDeletePanel.vue';
import usageReminderModal from '@/modules/AppAlerts/services/modals/usageReminder.modal';
import DynamicDataScheduleModal from '@/modules/DataSchedule/services/modals/dynamicDataSchedule.modal';

const CLIENT_TIMEZONE = Schedule.getBrowserTimeZone();

export default {
  components: {
    HTooltip,
    HButton,
    HDropdown,
    LastRun,
    slackScheduleButton,
    dataScheduleCell,
    scheduleLabel,
    FloatingDeletePanel,
  },
  props: {
    source: {
      type: Object,
      default: null,
    },
    isV3: {
      type: Boolean,
      default: false,
    },
    filterDest: {
      type: String,
      default: null,
    },
  },
  data () {
    return {
      loaded: false,
      schedules: [],
      currAjax: null,
    };
  },
  computed: {
    ...mapGetters('tenantSubscription', {
      isExceedTenantUsage: 'isExceedTenantUsage',
    }),
    columns () {
      if (this.isV3) {
        return ['id', 'data_schedule_title', 'schedule_title', 'destination', 'source_title', 'last_run', 'creator_name', 'actions'];
      }
      return ['id', 'data_schedule_title', 'schedule_title', 'destination', 'source_title', 'filters', 'last_run', 'creator_name', 'actions'];
    },
    decoratedSchedules () {
      const { filterDest } = this;
      return filter(this.schedules, s => {
        return isObject(s.schedule) && (!filterDest || s.dest_type === filterDest);
      }).map(ds => {
        ds.data_schedule_title = ds.title;
        ds.source_title = ds.source_title || '(Deleted source)';
        ds.schedule_title = ds.schedule ? ds.schedule.friendly_description : '(No schedule)';
        ds.source_info = this.buildSourceInfo(ds.source_type, ds.source_id, ds.source_version);
        ds.shortenSchedule = ds.schedule && ds.schedule.friendly_description.split(' (')[0];
        if (!ds.filter_values) ds.filter_values = [];
        if (!ds.dynamic_filter_presets) ds.dynamic_filter_presets = [];
        return ds;
      });
    },
    options () {
      return {
        sortable: ['id', 'data_schedule_title', 'creator_name', 'source_title', 'schedule_title'],
        headings: {
          id: 'ID',
          data_schedule_title: 'Title',
          schedule_title: 'Schedule',
          destination: 'Destinations',
          source_title: 'Report/Dashboard',
          creator_name: 'Creator',
          last_run: 'Last Run',
          actions: '',
        },
        columnsClasses: {
          data_schedule_title: 'title',
          schedule_title: 'schedule',
          destination: 'dest',
          filters: 'filters',
          source_title: 'source',
        },
        texts: {
          noResults: 'No Schedules',
        },
        perPage: this.source ? 1000 : 25,
        perPageValues: this.source ? [1000] : [25, 50, 100],
      };
    },
    showManageRecipients () {
      return window.H.current_user.role === 'admin'
               && this.loaded && !this.source && this.isEmailDest;
    },
    isEmailDest () {
      return this.filterDest === EMAIL_DEST_TYPE;
    },
  },
  mounted () {
    this.refresh();
  },
  methods: {
    newDynamicSchedule () {
      DynamicDataScheduleModal.open({ dest_type: 'EmailDest' }, this.source)
        .then(() => {
          this.refresh();
        });
    },
    isExcelPasswordProtected (ds) {
      return ds.dest_type === 'EmailDest' && ds.dest.options.format === 'excel' && ds.dest.options.password_enabled;
    },
    DestList (source) {
      return filter(DEST_LIST, Dest => (
        Dest.checkEnabled({ sourceType: get(source, 'source_type') })
      ));
    },
    buildSourceInfo (sourceType, id, sourceVersion) {
      if (sourceType === 'QueryReport') {
        return {
          icon: 'chart/table',
          href: `/queries/${id}`,
        };
      } if (sourceType === 'Dashboard') {
        return {
          icon: sourceVersion === 4 ? 'canvas' : 'dashboard',
          href: `/dashboards/${id}`,
        };
      }
      return {
        icon: 'chart/table',
        href: '#',
      };
    },
    editSchedule (ds) {
      ScheduleEditModal.open(ds.schedule, CLIENT_TIMEZONE)
        .then(res => {
          if (res.status !== 'resolved') {
            return;
          }
          const sc = JSON.parse(JSON.stringify(res.data));
          ds.schedule = sc;
          Schedule.fetchFriendlyDescription(ds.schedule.repeat)
            .then(data => {
              ds.schedule.friendly_description = data.result;
              Schedule.update(ds.schedule)
                .then(() => {
                  Notifier.success('Schedule updated successfully');
                  TableHelper.updateRow(this.schedules, ds);
                })
                .catch(err => {
                  Ajax.handleAjaxError(err, 'Error updating schedule');
                });
            });
        });
    },
    editDs (dsId) {
      const dataSchedule = find(this.schedules, (ds) => { return ds.id === dsId; });
      dataSchedule.destType = dataSchedule.dest_type;
      const editModal = [3, 4].includes(dataSchedule.source_version) ? DynamicDataScheduleModal : DataScheduleEditModal;

      const source = this.source ? this.source : { source_id: dataSchedule.source_id, source_type: dataSchedule.source_type };

      editModal.open(dataSchedule, source)
        .then(res => {
          if (res.status === 'resolved') {
            this.refreshDataSchedule(dsId);
          }
        })
        .catch(noop);
    },
    deleteDs (es) {
      EmailSchedules.delete(es.id)
        .then(() => {
          Notifier.success('Schedule has been deleted successfully!');
          TableHelper.deleteRow(this.schedules, es);
        })
        .catch(err => {
          Ajax.handleAjaxError(err, 'Failed to delete schedule');
        });
    },
    async sendDs (esId) {
      const confirmed = await Modal.confirm('Send now', 'This will execute this Data Schedule now.');
      if (!confirmed) {
        return;
      }
      try {
        const { job_id: jobId } = await EmailSchedules.execute(esId);
        Notifier.success('Schedule successfully queued for execution!');
        const lastRunJob = {
          id: jobId,
          status: 'created',
        };
        const executedSchedule = find(this.schedules, (schedule) => schedule.id === esId);
        if (!executedSchedule) {
          return;
        }
        executedSchedule.last_run_job = lastRunJob;
      } catch (err) {
        Ajax.handleAjaxError(err, 'Failed to queue schedule execution');
      }
    },
    newDs (destType = EMAIL_DEST_TYPE) {
      if (this.isExceedTenantUsage) {
        usageReminderModal();
        return;
      }
      DataScheduleEditModal.open({ dest_type: destType }, this.source)
        .then(() => {
          this.refresh();
        });
    },
    sendAll () {
      Modal.confirm('Send all?', 'This will execute all Data Schedules now.')
        .then(confirmed => {
          if (!confirmed) {
            return;
          }
          EmailSchedules.sendAll(this.source.source_id, this.source.source_type)
            .then(() => {
              Notifier.success('All schedules successfully queued for execution!');
            })
            .catch(err => {
              Ajax.handleAjaxError(err, 'Failed to send schedules');
            });
        });
    },
    // Only for email dest
    listRecipients () {
      emailScheduleRecipientModal.open()
        .then(() => {
          this.refresh();
        });
    },
    listBySource () {
      EmailSchedules.listBySource(this.source.source_id, this.source.source_type, null)
        .then(this._handle_success, this._handle_error);
    },
    list () {
      this.loaded = false;
      EmailSchedules.fetchAll(null)
        .then(this._handle_success, this._handle_error);
    },
    refresh () {
      if (this.source) {
        this.listBySource();
      } else {
        this.list();
      }
    },
    async refreshDataSchedule (esId) {
      try {
        const ds = await EmailSchedules.fetch(esId);
        ds.source_title = ds.source.title;
        ds.creator_name = ds.creator.name;
        await this.refreshLastRunJob(ds);
        TableHelper.updateRow(this.schedules, ds);
      } catch (err) {
        Ajax.handleAjaxError(err, 'Error loading schedule');
      }
    },
    async refreshLastRunJob (ds) {
      const lastRunJobs = await Jobs.fetchLastRunJobs({
        source_type: 'EmailSchedule', // Todo: rename to DataSchedule later
        source_method: 'execute',
        ids: [ds.id],
      });
      ds.last_run_job = lastRunJobs[ds.id];
    },
    _handle_success (dataSchedules) {
      this.schedules = dataSchedules;
      this.loaded = true;
    },
    _handle_error (response) {
      Ajax.handleAjaxError(response, 'Error loading list of schedules');
    },
    onScheduleUpdated (payload) {
      this.refreshDataSchedule(payload.source_id);
    },
  },
};
</script>

<style lang="scss">
.h-modal-body {
  .data-schedule-list {
    .h-table-wrapper {
      overflow-x: auto;
    }
  }
}
</style>
