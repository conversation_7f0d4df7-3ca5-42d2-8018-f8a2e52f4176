# frozen_string_literal: true

# typed: false

require 'rails_helper'

describe 'Canvas Dashboard Data Schedules', :js do
  include_context 'canvas_dashboard'
  let(:email_dest) do
    create(
      :email_dest,
      title: 'Em<PERSON> Dest',
      recipients: ['<EMAIL>'],
      options: {
        attachment_formats: ['excel_dashboard'],
        include_header: true,
      },
    )
  end
  let(:schedule) { create(:schedule, repeat: '* * * * *', paused: true) }
  let!(:data_schedule) do
    create(
      :email_schedule,
      tenant: admin.tenant,
      creator: admin,
      source: canvas_dashboard,
      dest: email_dest,
      schedule: schedule,
    )
  end

  before do
    FeatureToggle.toggle_global('ui:use_filter_select', true)
  end

  it 'workses' do
    safe_login(admin, dashboard_path(canvas_dashboard))
    wait_for_element_load('#block-v1')
    page.find('button', text: 'Settings').click
    page.find('[data-ci="ci-tab-dataSchedules"]').click
    wait_for_all_ajax_requests
    expect(page.find('.data-schedule-list').text).to eq("Filter:\nDestinations\nSchedule\nLast Run Info\<EMAIL>\nPaused\nNot Executed")
  end

  it 'edit should works' do
    safe_login(admin, dashboard_path(canvas_dashboard))
    page.find('button', text: 'Settings').click
    page.find('[data-ci="ci-tab-dataSchedules"]').click
    wait_for_all_ajax_requests
    safe_click('.ci-actions')
    page.find('.ci-es-edit').click
    expect(page.has_css?('.ci-include-header[data-hui-checked="true"]')).to eq(true)
  end

  context 'Schedule Title Feature' do
    context 'Dashboard Preferences - Schedule List' do
      it 'displays "(No title)" for schedules without titles' do
        safe_login(admin, dashboard_path(canvas_dashboard))
        wait_for_element_load('#block-v1')
        page.find('button', text: 'Settings').click
        page.find('[data-ci="ci-tab-dataSchedules"]').click
        wait_for_all_ajax_requests

        # Verify the schedule without title shows fallback text
        expect(page.find('[data-ci="ci-schedule-title-display"]').text).to eq('(No title)')
      end

      it 'displays custom title when schedule has one' do
        # Update the existing schedule with a title
        data_schedule.update!(title: 'Monthly Partner Report')

        safe_login(admin, dashboard_path(canvas_dashboard))
        wait_for_element_load('#block-v1')
        page.find('button', text: 'Settings').click
        page.find('[data-ci="ci-tab-dataSchedules"]').click
        wait_for_all_ajax_requests

        # Verify the custom title is displayed
        expect(page.find('[data-ci="ci-schedule-title-display"]').text).to eq('Monthly Partner Report')
      end
    end

    context 'Edit Existing Schedule' do
      it 'allows adding a title to an existing schedule without title' do
        safe_login(admin, dashboard_path(canvas_dashboard))
        page.find('button', text: 'Settings').click
        page.find('[data-ci="ci-tab-dataSchedules"]').click
        wait_for_all_ajax_requests

        # Open edit modal
        safe_click('.ci-actions')
        page.find('.ci-es-edit').click
        wait_for_element_load('[data-ci="ci-schedule-title-input"]')

        # Verify title input field is present and empty
        title_input = page.find('[data-ci="ci-schedule-title-input"]')
        expect(title_input.value).to be_empty

        # Add a title
        title_input.set('Weekly Sales Dashboard')

        # Save the schedule
        safe_click('.ci-submit-btn')
        wait_for_all_ajax_requests

        # Verify the title is saved and displayed
        expect(page.find('[data-ci="ci-schedule-title-display"]').text).to eq('Weekly Sales Dashboard')

        # Verify in database
        expect(data_schedule.reload.title).to eq('Weekly Sales Dashboard')
      end

      it 'allows modifying an existing title' do
        # Set initial title
        data_schedule.update!(title: 'Original Title')

        safe_login(admin, dashboard_path(canvas_dashboard))
        page.find('button', text: 'Settings').click
        page.find('[data-ci="ci-tab-dataSchedules"]').click
        wait_for_all_ajax_requests

        # Open edit modal
        safe_click('.ci-actions')
        page.find('.ci-es-edit').click
        wait_for_element_load('[data-ci="ci-schedule-title-input"]')

        # Verify existing title is loaded
        title_input = page.find('[data-ci="ci-schedule-title-input"]')
        expect(title_input.value).to eq('Original Title')

        # Modify the title
        title_input.set('Updated Title')

        # Save the schedule
        safe_click('.ci-submit-btn')
        wait_for_all_ajax_requests

        # Verify the updated title is displayed
        expect(page.find('[data-ci="ci-schedule-title-display"]').text).to eq('Updated Title')

        # Verify in database
        expect(data_schedule.reload.title).to eq('Updated Title')
      end

      it 'allows clearing an existing title' do
        # Set initial title
        data_schedule.update!(title: 'Title to be Cleared')

        safe_login(admin, dashboard_path(canvas_dashboard))
        page.find('button', text: 'Settings').click
        page.find('[data-ci="ci-tab-dataSchedules"]').click
        wait_for_all_ajax_requests

        # Open edit modal
        safe_click('.ci-actions')
        page.find('.ci-es-edit').click
        wait_for_element_load('[data-ci="ci-schedule-title-input"]')

        # Clear the title
        title_input = page.find('[data-ci="ci-schedule-title-input"]')
        title_input.set('')

        # Save the schedule
        safe_click('.ci-submit-btn')
        wait_for_all_ajax_requests

        # Verify fallback text is displayed
        expect(page.find('[data-ci="ci-schedule-title-display"]').text).to eq('(No title)')

        # Verify in database
        expect(data_schedule.reload.title).to be_blank
      end
    end
  end

  context 'analyst cannot schedule on unpermitted widgets' do
    before do
      admin.unshare(:all, 'read', ds)
    end

    let(:current_ability) do
      DashboardWidgetsAbility.new(analyst)
    end

    it 'is not able to select unpermitted widgets on schedule' do
      expect(current_ability.can?(:read, canvas_dashboard.viz_blocks.first)).to eq(false)
      safe_login(analyst, dashboard_path(canvas_dashboard))
      wait_for_element_load('#block-v1')
      page.find('button', text: 'Export').click
      page.find('div.cursor-pointer', text: 'Schedule Export').click
      wait_for_element_load('.data-schedule-modal')
      page.find('.widget-select').click
      expect(page.find('.h-popover .tree-select-options-container').text).to eq('No options')
    end
  end

  context 'Canvas Dashboard Data Schedules', :require_puppeteer do
    include_context 'dashboards_v4'

    let(:blocks) do
      [text_block, viz_block_table, filter_block]
    end

    let(:block_views) do
      {
        'text_block' => {
          'layer' => 0,
          'position' => { 'h' => 300, 'w' => 400, 'x' => 10, 'y' => 10 },
        },
        'v1' => {
          'layer' => 0,
          'position' => { 'h' => 300, 'w' => 400, 'x' => 10, 'y' => 310 },
        },
        'text_filter' => {
          'layer' => 0,
          'position' => { 'h' => 300, 'w' => 400, 'x' => 10, 'y' => 610 },
        },
      }
    end

    let(:dashboard) { dashboard_with_filter }

    before do
      Job.destroy_all
    end

    context 'Schedule Title Feature in V4 Modal' do
      it 'creates a new schedule with custom title' do
        qlogin(admin, "/dashboards/v4/#{dashboard.id}")
        # Click on export dropdown
        wait_for_element_load do
          page.find('[data-ci="ci-export-dropdown"]')
        end
        safe_click('[data-ci="ci-export-dropdown"]')
        # Open schedule export modal
        page.find('.ci-schedule-export').click
        wait_for_element_load do
          page.find('.email-dest-form')
        end

        # Verify title input field is present
        expect(page).to have_css('[data-ci="ci-schedule-title-input"]')

        # Enter a custom title
        page.find('[data-ci="ci-schedule-title-input"]').set('Q4 Revenue Dashboard')

        # Enter email
        page.find('.ci-es-recipients input').set('<EMAIL>')
        find('.ci-es-recipients input').native.send_keys(:return)

        # Set schedule frequency
        select_h_select_option('.schedule-repeat-by', value: 'Every')

        # Submit the schedule
        safe_click('.ci-submit-btn')

        # Wait for success notification
        expect_notifier_content(/successfully/)

        # Verify the schedule was created with the correct title
        created_schedule = EmailSchedule.last
        expect(created_schedule.title).to eq('Q4 Revenue Dashboard')
      end

      it 'creates a new schedule without title (empty field)' do
        qlogin(admin, "/dashboards/v4/#{dashboard.id}")
        # Click on export dropdown
        wait_for_element_load do
          page.find('[data-ci="ci-export-dropdown"]')
        end
        safe_click('[data-ci="ci-export-dropdown"]')
        # Open schedule export modal
        page.find('.ci-schedule-export').click
        wait_for_element_load do
          page.find('.email-dest-form')
        end

        # Leave title field empty (verify it's empty by default)
        title_input = page.find('[data-ci="ci-schedule-title-input"]')
        expect(title_input.value).to be_empty

        # Enter email
        page.find('.ci-es-recipients input').set('<EMAIL>')
        find('.ci-es-recipients input').native.send_keys(:return)

        # Set schedule frequency
        select_h_select_option('.schedule-repeat-by', value: 'Every')

        # Submit the schedule
        safe_click('.ci-submit-btn')

        # Wait for success notification
        expect_notifier_content(/successfully/)

        # Verify the schedule was created with blank title
        created_schedule = EmailSchedule.last
        expect(created_schedule.title).to be_blank
      end
    end

    context 'send test data schedule' do
      shared_examples 'submit test run' do
        it 'run test data schedule success' do
          qlogin(admin, "/dashboards/v4/#{dashboard.id}")
          # Click on export dropdown
          wait_for_element_load do
            page.find('[data-ci="ci-export-dropdown"]')
          end
          safe_click('[data-ci="ci-export-dropdown"]')
          # Open schedule export model
          page.find('.ci-schedule-export').click
          wait_for_element_load do
            page.find('.email-dest-form')
          end
          # Enter email
          page.find('.ci-es-recipients input').set('<EMAIL>')
          find('.ci-es-recipients input').native.send_keys(:return)
          safe_click('.ci-test-run-es')
          # wait for test status to be success
          wait_expect(true, 50) do
            page.first('[data-ci="ci-job-status"]')&.text == 'Success'
          end
        end
      end

      context 'dashboard include filter, text and viz block' do
        it_behaves_like 'submit test run'
      end

      context 'when block views empty (fallback block position in sort widget works)' do
        let(:block_views) { {} }

        it_behaves_like 'submit test run'
      end

      context 'create once time data schedule correct' do
        it 'workses' do
          qlogin(admin, "/dashboards/v4/#{dashboard.id}")
          # Click on export dropdown
          wait_for_element_load do
            page.find('[data-ci="ci-export-dropdown"]')
          end
          safe_click('[data-ci="ci-export-dropdown"]')
          # Open schedule export model
          safe_click('.ci-schedule-export')
          wait_for_element_load do
            page.find('.email-dest-form')
          end
          # Enter email
          page.find('.ci-es-recipients input').set('<EMAIL>')
          find('.ci-es-recipients input').native.send_keys(:return)
          # Set schedule to once time
          select_h_select_option('.schedule-repeat-by', value: 'Once')
          safe_click('.ci-submit-btn')
          schedule_request = extract_ajax_requests({ url: /\/submit_execute/ }).last
          # Select once time data schedule, the body is test_data_schedule
          expect(schedule_request['data']['test_data_schedule']).to be_present
          # wait for test status to be success
          wait_expect(true, 30) do
            page.first('.ci-test-status .label')&.text == 'Success'
          end
        end
      end
    end

    context 'dynamic variables' do
      context 'enable feature toggle' do
        before do
          FeatureToggle.toggle_global(EmailSchedule::FT_ENABLE_CUSTOM_EMAIL_DYNAMIC_MESSAGE, true)
        end

        it 'create email data schedule with dynamic variables' do
          qlogin(admin, "/dashboards/v4/#{dashboard.id}")
          # Click on export dropdown
          wait_for_element_load do
            page.find('[data-ci="ci-export-dropdown"]')
          end
          safe_click('[data-ci="ci-export-dropdown"]')
          # Open schedule export model
          safe_click('.ci-schedule-export')
          wait_for_element_load do
            page.find('.email-dest-form')
          end
          # Enter email
          page.find('.ci-es-recipients input').set('<EMAIL>')
          find('.ci-es-recipients input').native.send_keys(:return)
          select_h_select_option('.schedule-repeat-by', value: 'Every')
          safe_click('.ci-submit-btn')

          # wait for notify su
          expect_notifier_content(/successfully/)
          email_dest = EmailDest.where('recipients @> ?', ['<EMAIL>'].to_json).last
          expect(email_dest.options[:can_use_dynamic_variables]).to be(true)
          expect(email_dest.title).to eq('{{$dashboard_title}} ')
        end

        it 'correct handle interaction with variables' do
          qlogin(admin, "/dashboards/v4/#{dashboard.id}")
          # Click on export dropdown
          wait_for_element_load do
            page.find('[data-ci="ci-export-dropdown"]')
          end
          safe_click('[data-ci="ci-export-dropdown"]')
          # Open schedule export model
          safe_click('.ci-schedule-export')
          wait_for_element_load do
            page.find('.email-dest-form')
          end
          # Enter email
          page.find('.ci-es-recipients input').set('<EMAIL>')
          find('.ci-es-recipients input').native.send_keys(:return)
          select_h_select_option('.schedule-repeat-by', value: 'Every')

          safe_click('.toggle-email-dest-form')
          # Add in last editor when not focus in editor
          safe_click('[data-ci="ci-add-variable-btn"]')
          select_h_select_option('[data-ci="ci-add-variable-select"]', value: 'dashboard_title')
          wait_expect(true) do
            page.find('[data-ci="subject-dynamic-message"] [data-ci="ci-dynamic-message-editor"]').native.attribute('innerHTML') == '<span contenteditable="false" class="mention-variable">{{$dashboard_title}}</span> <span contenteditable="false" class="mention-variable">{{$dashboard_title}}</span>'
          end

          editor = find('[data-ci="subject-dynamic-message"] [data-ci="ci-dynamic-message-editor"]')
          editor.execute_script(<<-JS)
            this.innerHTML = "test"; // Clear and set new text
          JS

          # Move cursor to the start
          editor.execute_script(<<-JS)
            this.focus();
            const range = document.createRange();
            const sel = window.getSelection();
            if (this.firstChild) {
              range.setStart(this.firstChild, 0); // Move cursor to the start
              range.collapse(true);
              sel.removeAllRanges();
              sel.addRange(range);
            }
          JS

          # restore correct cursor position (in this case is start position)
          safe_click('[data-ci="ci-add-variable-btn"]')
          select_h_select_option('[data-ci="ci-add-variable-select"]', value: 'dashboard_title')

          wait_expect(true) do
            page.find('[data-ci="subject-dynamic-message"] [data-ci="ci-dynamic-message-editor"]').native.attribute('innerHTML') == '<span contenteditable="false" class="mention-variable">{{$dashboard_title}}</span>test'
          end
        end

        context 'slack' do
          let(:admin) { get_test_admin }
          let(:team_info) do
            {
              slack_settings: {
                slack_team_id: 'T01EGCAN5C2',
                slack_enabled: true,
              },
              slack_team_info: {
                id: 'T01EGCAN5C2',
                name: 'Holistics Test',
                url: 'https://holisticstestgroup.slack.com/',
                domain: 'holisticstestgroup',
                email_domain: 'holistics.io',
                icon: {
                  image_default: true,
                  image_34: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-34.png',
                  image_44: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-44.png',
                  image_68: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-68.png',
                  image_88: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-88.png',
                  image_102: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-102.png',
                  image_230: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-230.png',
                  image_132: 'https://a.slack-edge.com/80588/img/avatars-teams/ava_0002-132.png',
                },
                avatar_base_url: 'https://ca.slack-edge.com/',
                is_verified: false,
                lob_sales_home_enabled: false,
              },
            }
          end

          before do
            FeatureToggle.toggle_global('integrations:slack', true)
            FeatureToggle.toggle_global('slack_schedules:dashboard', true)
            FactoryBot.create(:slack_authorization, user_id: admin.id)
            update_tenant_slack_settings(admin.tenant, true, 'T01EGCAN5C2')
            VcrHelper.ignore_hosts('127.0.0.1', 'localhost')
            allow(Slack::SlackService).to receive(:get_team_info).with(user: admin).and_return(team_info)
            FeatureToggle.toggle_global(SlackDest::FT_ENABLE_CUSTOM_SLACK_DYNAMIC_MESSAGE, true)
          end

          it 'create slack data schedule with dynamic variables' do
            qlogin(admin, "/dashboards/v4/#{dashboard.id}")
            # Click on export dropdown
            wait_for_element_load do
              page.find('[data-ci="ci-export-dropdown"]')
            end
            safe_click('[data-ci="ci-export-dropdown"]')
            # Open schedule export model

            safe_click('.ci-schedule-export')
            safe_click('.ci-slack-dest')

            VCR.use_cassette('slack/list_all_channels') do
              VCR.use_cassette('slack/get_team_info') do
                wait_for_element_load('.ci-channels-dropdown')
                wait_for_element_load('.ci-filter-labels-list')
                page.find('.ci-channels-dropdown .hui-select-trigger').click
                page.find('.ci-channels-dropdown .hui-select-trigger input').set('CHANNEL_ID')
                wait_for_element_load('.hui-select-option.focused')
                wait_for_element_load('[data-value="$$add_new_channel$$"]')
                page.find('.ci-channels-dropdown .hui-select-trigger input').send_keys(:enter)
              end
            end

            safe_click('[data-ci="ci-slack-dynamic-message"]')
            wait_expect(1) { page.all('.customize-slack-content').count }
            safe_click('.ci-submit-btn')

            wait_expect(1) { page.all('table.hui-table > tbody > tr').count }

            slack_dest = SlackDest.find_by(slack_team_id: 'T01EGCAN5C2')
            wait_expect(true) { slack_dest.options['can_use_dynamic_variables'] }
          end

          context 'edit' do
            let(:data_schedule) do
              FactoryBot.create(:email_schedule, dest: dest, source: dashboard, tenant_id: admin.tenant_id)
            end
            let(:dest) do
              FactoryBot.create(:slack_dest, title: 'ahihi', message: ':bong:',
                                             slack_channels: [{ id: 'CHANNEL_ID_2', name: 'private', type: 'public' }], options: { 'can_use_dynamic_variables' => true, 'attachment_formats' => ['png'] },)
            end

            it 'can edit slack schedule successful' do
              qlogin(admin, "/dashboards/v4/#{dashboard.id}")
              # Click on export dropdown
              wait_for_element_load do
                page.find('[data-ci="ci-export-dropdown"]')
              end
              safe_click('[data-ci="ci-export-dropdown"]')
              safe_click('.ci-manage-data-schedules')
              # Open schedule export model
              wait_for_element_load('.data-schedule-list')

              VCR.use_cassette('slack/list_all_channels') do
                VCR.use_cassette('slack/get_team_info') do
                  safe_click('.ci-actions')
                  safe_click('.ci-es-edit')
                  wait_for_element_load('.ci-channels-dropdown')
                  wait_for_element_load('.ci-filter-labels-list')
                  expect(page.find('.ci-channels-dropdown .hui-select-trigger').text).to eq('private')
                end
              end

              safe_click('[data-ci="ci-slack-dynamic-message"]')
              wait_expect(1) { page.all('.customize-slack-content').count }
              wait_expect(':bong:') { page.find('[data-ci="ci-preview-slack-message"]').text }

              page.find('[data-ci="ci-slack-dynamic-message"] [data-ci="ci-dynamic-message-editor"]').set('{{$dashboard_title}}')
              wait_expect(dashboard.title) { page.find('[data-ci="ci-preview-slack-message"]').text }

              safe_click('.ci-submit-btn')
              wait_expect(1) { page.all('table.hui-table > tbody > tr').count }

              slack_dest = SlackDest.find_by(id: dest.id)
              wait_expect('{{$dashboard_title}}') { slack_dest.message }
              wait_expect(true) { slack_dest.options['can_use_dynamic_variables'] }
            end
          end
        end
      end

      context 'disable feature toggle' do
        before do
          FeatureToggle.toggle_global(EmailSchedule::FT_ENABLE_CUSTOM_EMAIL_DYNAMIC_MESSAGE, false)
        end

        it 'create data schedule without dynamic variables' do
          qlogin(admin, "/dashboards/v4/#{dashboard.id}")
          # Click on export dropdown
          wait_for_element_load do
            page.find('[data-ci="ci-export-dropdown"]')
          end
          safe_click('[data-ci="ci-export-dropdown"]')
          # Open schedule export model
          safe_click('.ci-schedule-export')
          wait_for_element_load do
            page.find('.email-dest-form')
          end
          # Enter email
          page.find('.ci-es-recipients input').set('<EMAIL>')
          find('.ci-es-recipients input').native.send_keys(:return)
          select_h_select_option('.schedule-repeat-by', value: 'Every')
          safe_click('.ci-submit-btn')

          # wait for notify su
          expect_notifier_content(/successfully/)
          email_dest = EmailDest.where('recipients @> ?', ['<EMAIL>'].to_json).last
          expect(email_dest.options[:can_use_dynamic_variables]).to be(false)
          expect(email_dest.title).to eq('')
        end
      end
    end
  end
end
